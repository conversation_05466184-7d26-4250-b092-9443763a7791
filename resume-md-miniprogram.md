# 潘慧文
**小程序开发工程师 | 大前端开发工程师**

📱 15354872767 (微信同号) | ✉️ <EMAIL> | 📍 北京 | 🎂 1992年2月 | 🧑🏻‍💻 男

---

## 💼 工作经验

### 移动开发工程师 | 内蒙古博睿达网络科技有限公司
**2024年6月 - 至今 (在职)**

#### 核心项目成果
- **特教公益小程序** (2024至今)
  - 与厦门特教基金会合作，采用uni-app开发公益类小程序
  - 实现标准模块化设计，支持站点配置和内容结构化配置
  - 兼容用户、管理员双身份，涵盖益卖商城、捐赠系统、社区活动、IM等功能
  - **成果**: 成功上线并获得基金会认可

- **企业内部服务小程序矩阵** (2024至今)
  - 开发集团企业内部服务系统和员工优惠购平台
  - 包含内部报修、会议室预定、餐饮预订等效率工具
  - **成果**: 提升企业内部运营效率，获得客户好评

#### 技术架构与项目管理
- **小程序开发**: uni-app跨平台开发，支持模块化配置和多身份权限
- **架构设计**: 标准化模块设计，内容结构化配置，可复用组件库
- **产品矩阵**: 定制化企业内购小程序、内部服务小程序

#### 主要产品线
- 特教公益类小程序（与厦门特教基金会合作）
- 企业内部服务小程序
- 定制化内购小程序

### 移动开发工程师 | 北京牛投邦科技咨询有限公司
**2018年8月 - 2024年4月 (5年8个月)**

#### 核心项目成果
- **小程序产品矩阵** (2018-2024)
  - 开发获客排行榜小程序、鹰眼获客能手小程序等多款产品
  - 使用微信原生、Taro、uni-app等技术栈，实现小程序模板化架构
  - 支持多角色模板切换、云开发、送审等完整流程
  - **成果**: 成功上线多款小程序产品，服务多家头部基金客户

- **多角色权限系统重构** (2020-2024)
  - 主导多家头部基金客户端从单角色升级为多角色多权限架构
  - 设计并实现复杂的角色切换、权限控制、状态管理系统
  - 通过设计模式优化，实现7种不同权限判别动作的统一管理
  - **成果**: 项目如期交付，获得客户高度认可

#### 技术架构与项目管理
- **小程序**: 微信原生、Taro、uni-app多平台开发，模板化架构设计
- **移动端**: React Native客户端开发，支持热更新、支付、社会化分享
- **后台管理**: PC端管理系统开发，完整的前后端分离架构
- **项目模板化**: 主导基于WBS的项目模板开发，提升团队开发效率

#### 主要产品线
- 获客排行榜小程序、鹰眼获客能手小程序
- 智能营销获客管理系统、栗子理财师、理财顾问云
- 多家头部基金公司移动端解决方案

### 移动开发工程师 | 北京天云智慧科技有限公司
**2017年6月 - 2018年8月 (1年2个月)**

#### 核心项目成果
- **内部招聘系统** (2017-2018)
  - 独立开发内部招聘客户端和招聘管理端
  - 全栈React Native开发，从0到1完成产品交付
  - **成果**: 成功交付完整的招聘管理系统

#### 技术架构与项目管理
- **移动端**: React Native全栈开发
- **项目管理**: 独立负责产品设计、开发、测试、上线全流程

---

## 🛠️ 技术栈

### 前端技术
- **小程序开发**: 微信原生小程序、uni-app、Taro (5年+)
- **Web前端**: Vue、React、JavaScript (ES6+)、HTML5、CSS3
- **移动端**: React Native (5年+)
- **后端技术**: Node.js
- **构建工具**: Webpack、Vite、CI/CD流水线

### 开发工具与流程
- **IDE**: WebStorm、VS Code
- **版本控制**: Git (团队协作、代码审查)
- **项目管理**: 敏捷开发、结果导向的项目交付

### 核心能力
- 数据结构与算法、设计模式应用
- 独立开发能力、代码规范化
- 性能优化、架构设计

---

## 🏢 行业经验

**金融科技领域** (8年经验)
- 财富管理、证券、保险、基金、理财产品
- 熟悉金融业务流程和合规要求
- 具备完整的金融产品开发生命周期经验

**教育科技与公益领域**
- 特教公益小程序开发与运营
- 企业定制化服务解决方案
- 内部效率工具开发

---

## 🎯 核心优势

### 技术能力
- 擅长小程序全栈开发和架构设计
- 具备从0到1的产品开发经验
- 熟悉复杂业务场景的技术解决方案设计

### 问题解决能力
- 成功解决多角色权限系统的复杂业务逻辑
- 通过技术手段显著提升产品性能和用户体验
- 具备较强的技术难点攻克能力

### 团队协作
- 良好的沟通协调能力，跨部门合作经验丰富
- 责任心强，以结果为导向的工作方式
- 有内部技术分享经验，推动团队技术成长

---

## 🎓 教育背景

**本科学士学位** | 软件工程专业
内蒙古财经大学

---

## 📋 求职信息

- **期望职位**: 小程序开发工程师 / 大前端开发工程师
- **工作地点**: 北京
- **到岗时间**: 5-7个工作日
- **工作年限**: 8年 (2016年6月至今)